# 说明书查询API使用指南

## 概述

本项目新增了7个说明书查询API接口，用于前端系统查询项目的聚合说明书、流程说明书和方法内容。

## 快速开始

### 1. 启动应用

```bash
cd code-graph-documentation
./gradlew bootRun
```

应用将在 `http://localhost:8080/documentation` 启动。

### 2. 接口基础URL

```
http://localhost:8080/documentation/api/documentation/query
```

## 接口使用示例

### 1. 查询所有子系统

```bash
curl -X GET "http://localhost:8080/documentation/api/documentation/query/projects"
```

### 2. 查询项目的聚合说明书

```bash
curl -X GET "http://localhost:8080/documentation/api/documentation/query/projects/mall-portal/aggregated-documentations"
```

### 3. 查询聚合说明书下的流程说明书

```bash
curl -X GET "http://localhost:8080/documentation/api/documentation/query/aggregated-documentations/agg_doc_001/process-documentations"
```

### 4. 查询聚合说明书内容

```bash
curl -X GET "http://localhost:8080/documentation/api/documentation/query/aggregated-documentations/agg_doc_001/content"
```

### 5. 查询流程说明书内容

```bash
curl -X GET "http://localhost:8080/documentation/api/documentation/query/process-documentations/doc_001/content"
```

### 6. 分页查询流程说明书的方法ID

```bash
curl -X GET "http://localhost:8080/documentation/api/documentation/query/process-documentations/doc_001/methods?page=1&size=20"
```

### 7. 查询方法内容

```bash
curl -X GET "http://localhost:8080/documentation/api/documentation/query/methods/method_001/content"
```

## 前端集成示例

### JavaScript/TypeScript

```typescript
// API基础配置
const API_BASE_URL = 'http://localhost:8080/documentation/api/documentation/query';

// 查询所有项目
async function getAllProjects() {
  const response = await fetch(`${API_BASE_URL}/projects`);
  const result = await response.json();
  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 查询项目的聚合说明书
async function getAggregatedDocumentations(projectId: string) {
  const response = await fetch(`${API_BASE_URL}/projects/${projectId}/aggregated-documentations`);
  const result = await response.json();
  return result.success ? result.data : [];
}

// 分页查询方法ID
async function getMethodIds(documentationId: string, page: number = 1, size: number = 10) {
  const response = await fetch(
    `${API_BASE_URL}/process-documentations/${documentationId}/methods?page=${page}&size=${size}`
  );
  const result = await response.json();
  return result.success ? result.data : null;
}

// 查询方法内容
async function getMethodContent(methodId: string) {
  const response = await fetch(`${API_BASE_URL}/methods/${methodId}/content`);
  const result = await response.json();
  return result.success ? result.data : null;
}
```

### React Hook示例

```typescript
import { useState, useEffect } from 'react';

// 自定义Hook：查询项目列表
export function useProjects() {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    getAllProjects()
      .then(setProjects)
      .catch(setError)
      .finally(() => setLoading(false));
  }, []);

  return { projects, loading, error };
}

// 自定义Hook：分页查询方法ID
export function useMethodIds(documentationId: string, page: number, size: number) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (documentationId) {
      setLoading(true);
      getMethodIds(documentationId, page, size)
        .then(setData)
        .finally(() => setLoading(false));
    }
  }, [documentationId, page, size]);

  return { data, loading };
}
```

## 数据结构说明

### ProjectInfo
```typescript
interface ProjectInfo {
  projectId: string;          // 项目ID
  projectName?: string;       // 项目名称（可选）
  documentationCount: number; // 聚合说明书数量
}
```

### AggregatedDocumentationSummary
```typescript
interface AggregatedDocumentationSummary {
  id: string;              // 聚合说明书ID
  title: string;           // 标题
  summary: string;         // 摘要
  projectId: string;       // 项目ID
  branchName: string;      // 分支名称
  aggregationType: string; // 聚合类型
}
```

### ProcessDocumentationSummary
```typescript
interface ProcessDocumentationSummary {
  id: string;           // 流程说明书ID
  title: string;        // 标题
  summary: string;      // 摘要
  entryPointId: string; // 入口点ID
  entryPointName: string; // 入口点名称
  projectId: string;    // 项目ID
  branchName: string;   // 分支名称
}
```

### MethodContent
```typescript
interface MethodContent {
  methodId: string;       // 方法ID
  fullName: string;       // 方法全限定名
  methodName: string;     // 方法名
  className: string;      // 类名
  signature: string;      // 方法签名
  content: string;        // 方法内容（源代码）
  description?: string;   // 方法描述
  returnType?: string;    // 返回类型
  parameters?: string;    // 参数列表
  isEntryPoint?: boolean; // 是否为入口点
  filePath?: string;      // 文件路径
}
```

### PageResult
```typescript
interface PageResult<T> {
  data: T[];           // 数据列表
  page: number;        // 当前页码
  size: number;        // 每页大小
  total: number;       // 总记录数
  totalPages: number;  // 总页数
  hasNext: boolean;    // 是否有下一页
  hasPrevious: boolean; // 是否有上一页
}
```

## 错误处理

所有接口都返回统一的响应格式：

```typescript
interface ApiResponse<T> {
  success: boolean;  // 是否成功
  message: string;   // 响应消息
  data: T;          // 响应数据
}
```

建议在前端统一处理错误：

```typescript
async function apiCall(url: string) {
  try {
    const response = await fetch(url);
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }
    
    return result.data;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
}
```

## 性能优化建议

1. **缓存策略**: 对于不经常变化的数据（如项目列表），建议在前端进行缓存
2. **分页加载**: 对于大量数据，使用分页接口避免一次性加载过多数据
3. **懒加载**: 方法内容较大，建议按需加载
4. **防抖处理**: 对于搜索等频繁操作，建议添加防抖处理

## 注意事项

1. 方法内容会自动解压缩，无需前端处理
2. 分页查询的页码从1开始
3. 建议添加适当的加载状态和错误提示
4. 大文件内容可能较大，注意网络超时设置
