# 接口修改说明

## 修改内容

### 接口2：查询子系统下所有聚合说明书

**修改前**:
- 请求方式：`GET`
- 请求路径：`/projects/{projectId}/aggregated-documentations`
- 参数方式：路径参数 `@PathVariable String projectId`

**修改后**:
- 请求方式：`POST`
- 请求路径：`/projects/aggregated-documentations`
- 参数方式：请求体参数 `@RequestBody ProjectQueryRequest request`

## 新增文件

### ProjectQueryRequest.java
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectQueryRequest {
    @NotBlank(message = "项目ID不能为空")
    private String projectId;
}
```

## 请求示例

### cURL示例
```bash
curl -X POST "http://localhost:8080/documentation/api/documentation/query/projects/aggregated-documentations" \
  -H "Content-Type: application/json" \
  -d '{"projectId": "mall-portal"}'
```

### JavaScript示例
```javascript
async function getAggregatedDocumentations(projectId) {
  const response = await fetch(`${API_BASE_URL}/projects/aggregated-documentations`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ projectId })
  });
  const result = await response.json();
  return result.success ? result.data : [];
}
```

### 请求体格式
```json
{
  "projectId": "string"  // 项目ID，必填
}
```

### 响应格式
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": "agg_doc_001",
      "title": "订单管理流程聚合说明书",
      "summary": "包含订单创建、支付、发货等核心流程的聚合说明书",
      "projectId": "mall-portal",
      "branchName": "main",
      "aggregationType": "订单流程"
    }
  ]
}
```

## 修改的文件列表

1. **Controller层**:
   - `DocumentationQueryApiController.java` - 修改接口方法

2. **请求DTO**:
   - `ProjectQueryRequest.java` - 新增请求DTO

3. **测试文件**:
   - `DocumentationQueryApiControllerTest.java` - 更新测试用例

4. **文档文件**:
   - `API文档.md` - 更新接口文档
   - `README_API.md` - 更新使用示例

## 优势

1. **统一性**: POST方法更适合复杂的查询参数
2. **扩展性**: 后续可以轻松添加更多查询条件（如分支名称、状态等）
3. **安全性**: 避免敏感信息出现在URL中
4. **参数验证**: 可以使用Jakarta Validation进行参数校验

## 注意事项

1. 前端调用时需要修改请求方式为POST
2. 需要设置正确的Content-Type头部
3. 参数需要放在请求体中而不是URL路径中
4. 保持向后兼容性，如果有其他系统在使用旧接口，需要考虑渐进式迁移
