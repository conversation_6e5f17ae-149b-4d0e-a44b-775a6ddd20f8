# 说明书查询API接口文档

## 概述

本文档描述了说明书查询系统提供的7个REST API接口，用于查询项目的聚合说明书、流程说明书和方法内容。

**基础URL**: `http://localhost:8080/documentation/api/documentation/query`

## 接口列表

### 1. 查询所有已接入的子系统

**接口描述**: 查询所有已接入的子系统列表

**请求方式**: `GET`

**请求路径**: `/projects`

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "projectId": "mall-portal",
      "projectName": null,
      "documentationCount": 5
    },
    {
      "projectId": "mall-admin",
      "projectName": null,
      "documentationCount": 3
    }
  ]
}
```

### 2. 查询子系统下所有聚合说明书

**接口描述**: 查询指定项目下的所有聚合说明书摘要信息

**请求方式**: `POST`

**请求路径**: `/projects/{projectId}/aggregated-documentations`

**路径参数**:
- `projectId` (string, 必填): 项目ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": "agg_doc_001",
      "title": "订单管理流程聚合说明书",
      "summary": "包含订单创建、支付、发货等核心流程的聚合说明书",
      "projectId": "mall-portal",
      "branchName": "main",
      "aggregationType": "订单流程"
    }
  ]
}
```

### 3. 查询指定聚合说明书下所有流程说明书

**接口描述**: 查询指定聚合说明书下关联的所有流程说明书摘要信息

**请求方式**: `GET`

**请求路径**: `/aggregated-documentations/{aggregatedDocumentationId}/process-documentations`

**路径参数**:
- `aggregatedDocumentationId` (string, 必填): 聚合说明书ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": "doc_001",
      "title": "订单创建流程说明书",
      "summary": "详细描述用户下单的完整流程",
      "entryPointId": "method_001",
      "entryPointName": "createOrder",
      "projectId": "mall-portal",
      "branchName": "main"
    }
  ]
}
```

### 4. 根据聚合说明书ID查询说明书内容

**接口描述**: 查询指定聚合说明书的完整内容

**请求方式**: `GET`

**请求路径**: `/aggregated-documentations/{aggregatedDocumentationId}/content`

**路径参数**:
- `aggregatedDocumentationId` (string, 必填): 聚合说明书ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": "# 订单管理流程聚合说明书\n\n## 概述\n本说明书聚合了订单相关的核心流程...\n\n## 主要流程\n### 1. 订单创建流程\n..."
}
```

### 5. 根据流程说明书ID查询说明书内容

**接口描述**: 查询指定流程说明书的完整内容

**请求方式**: `GET`

**请求路径**: `/process-documentations/{documentationId}/content`

**路径参数**:
- `documentationId` (string, 必填): 流程说明书ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": "# 订单创建流程说明书\n\n## 流程概述\n用户通过前端页面提交订单信息...\n\n## 详细步骤\n1. 参数验证\n2. 库存检查\n..."
}
```

### 6. 根据流程说明书ID查询关联的所有方法ID（分页）

**接口描述**: 分页查询指定流程说明书关联的所有方法ID

**请求方式**: `GET`

**请求路径**: `/process-documentations/{documentationId}/methods`

**路径参数**:
- `documentationId` (string, 必填): 流程说明书ID

**查询参数**:
- `page` (integer, 可选): 页码，从1开始，默认为1
- `size` (integer, 可选): 每页大小，默认为10

**请求示例**: `/process-documentations/doc_001/methods?page=1&size=20`

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "data": [
      "method_001",
      "method_002",
      "method_003"
    ],
    "page": 1,
    "size": 20,
    "total": 45,
    "totalPages": 3,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

### 7. 根据方法ID查询方法内容

**接口描述**: 查询指定方法的详细内容，包括源代码（自动解压缩）

**请求方式**: `GET`

**请求路径**: `/methods/{methodId}/content`

**路径参数**:
- `methodId` (string, 必填): 方法ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "methodId": "method_001",
    "fullName": "com.macro.mall.portal.controller.OmsPortalOrderController.generateOrder",
    "methodName": "generateOrder",
    "className": "OmsPortalOrderController",
    "signature": "public CommonResult<Map<String, Object>> generateOrder(@RequestBody OrderParam orderParam)",
    "content": "@ApiOperation(\"根据购物车信息生成确认单信息\")\n@RequestMapping(value = \"/generateConfirmOrder\", method = RequestMethod.POST)\n@ResponseBody\npublic CommonResult<Map<String, Object>> generateOrder(@RequestBody OrderParam orderParam) {\n    Map<String, Object> result = portalOrderService.generateConfirmOrder(orderParam);\n    return CommonResult.success(result);\n}",
    "description": "根据购物车信息生成确认单信息",
    "returnType": "CommonResult<Map<String, Object>>",
    "parameters": "OrderParam orderParam",
    "isEntryPoint": true,
    "filePath": "/src/main/java/com/macro/mall/portal/controller/OmsPortalOrderController.java"
  }
}
```

## 错误响应格式

当接口调用失败时，返回统一的错误响应格式：

```json
{
  "success": false,
  "message": "错误描述信息",
  "data": null
}
```

## HTTP状态码

- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误

## 注意事项

1. 所有接口都返回JSON格式的响应
2. 分页查询的页码从1开始
3. 方法内容会自动进行解压缩处理
4. 建议在生产环境中添加适当的缓存机制
5. 大量数据查询时建议使用分页接口
