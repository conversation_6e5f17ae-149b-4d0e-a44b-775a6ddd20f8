package com.puti.code.documentation.repository.graph;

import com.puti.code.base.util.ContentCompressor;
import com.puti.code.documentation.controller.response.MethodContent;
import com.puti.code.repository.nebula.NebulaGraphClient;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 方法信息图数据库Repository
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
public class MethodRepository {
    
    @Autowired
    private NebulaGraphClient nebulaGraphClient;
    
    /**
     * 根据方法ID查询方法内容
     * 
     * @param methodId 方法ID
     * @return 方法内容
     */
    public MethodContent getMethodContent(String methodId) {
        try {
            log.info("开始查询方法内容，方法ID: {}", methodId);
            
            // 构建查询语句
            String query = """
                    MATCH (v:function) 
                    WHERE id(v) == "%s" 
                    RETURN v.function.full_name as fullName,
                           v.function.name as methodName,
                           v.function.content as content,
                           v.function.is_entry_point as isEntryPoint
                    """.formatted(methodId);
            
            ResultSet resultSet = nebulaGraphClient.execute(query);
            
            if (!resultSet.isSucceeded()) {
                log.error("方法内容查询失败: {}", resultSet.getErrorMessage());
                return null;
            }
            
            if (resultSet.rowsSize() == 0) {
                log.warn("未找到方法ID为 {} 的方法", methodId);
                return null;
            }
            
            // 解析查询结果
            return parseMethodContent(methodId, resultSet);
            
        } catch (Exception e) {
            log.error("查询方法内容时发生错误，方法ID: {}", methodId, e);
            return null;
        }
    }
    
    /**
     * 解析方法内容查询结果
     */
    private MethodContent parseMethodContent(String methodId, ResultSet resultSet) {
        try {
            // 获取第一行数据
            ResultSet.Record record = resultSet.rowValues(0);

            // 获取各个字段值（按照查询语句中的顺序）
            String fullName = getStringValue(record, 0);
            String methodName = getStringValue(record, 1);
            String compressedContent = getStringValue(record, 2);
            Boolean isEntryPoint = getBooleanValue(record, 3);

            // 解压缩内容
            String content = null;
            if (compressedContent != null && !compressedContent.isEmpty()) {
                try {
                    content = ContentCompressor.decompress(compressedContent);
                } catch (Exception e) {
                    log.warn("解压缩方法内容失败，方法ID: {}, 错误: {}", methodId, e.getMessage());
                    content = compressedContent; // 如果解压失败，使用原始内容
                }
            }

            return MethodContent.builder()
                    .methodId(methodId)
                    .fullName(fullName)
                    .methodName(methodName)
                    .content(content)
                    .isEntryPoint(isEntryPoint)
                    .build();

        } catch (Exception e) {
            log.error("解析方法内容时发生错误，方法ID: {}", methodId, e);
            return null;
        }
    }
    
    /**
     * 从结果集中获取字符串值
     */
    private String getStringValue(ResultSet.Record record, int index) {
        try {
            ValueWrapper value = record.get(index);
            if (value != null && value.isString()) {
                return value.asString();
            }
            return null;
        } catch (Exception e) {
            log.debug("获取字符串值失败，索引: {}, 错误: {}", index, e.getMessage());
            return null;
        }
    }

    /**
     * 从结果集中获取布尔值
     */
    private Boolean getBooleanValue(ResultSet.Record record, int index) {
        try {
            ValueWrapper value = record.get(index);
            if (value != null && value.asBoolean()) {
                return value.asBoolean();
            }
            return null;
        } catch (Exception e) {
            log.debug("获取布尔值失败，索引: {}, 错误: {}", index, e.getMessage());
            return null;
        }
    }
}
