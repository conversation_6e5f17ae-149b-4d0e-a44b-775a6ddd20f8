package com.puti.code.documentation.controller.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 方法内容响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MethodContent {
    
    /**
     * 方法ID
     */
    private String methodId;
    
    /**
     * 方法全限定名
     */
    private String fullName;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法签名
     */
    private String signature;
    
    /**
     * 方法内容（解压后的源代码）
     */
    private String content;
    
    /**
     * 方法描述/注释
     */
    private String description;
    
    /**
     * 返回类型
     */
    private String returnType;
    
    /**
     * 参数列表
     */
    private String parameters;
    
    /**
     * 是否为入口点
     */
    private Boolean isEntryPoint;
    
    /**
     * 文件路径
     */
    private String filePath;
}
