package com.puti.code.documentation.controller;

import com.puti.code.documentation.controller.response.*;
import com.puti.code.documentation.dto.PageRequest;
import com.puti.code.documentation.dto.PageResult;
import com.puti.code.documentation.service.DocumentationQueryApiService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 说明书查询API控制器测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DocumentationQueryApiControllerTest {
    
    @Mock
    private DocumentationQueryApiService documentationQueryApiService;
    
    @InjectMocks
    private DocumentationQueryApiController controller;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Test
    void testGetAllProjects() {
        // 准备测试数据
        List<ProjectInfo> mockProjects = List.of(
                ProjectInfo.builder()
                        .projectId("test-project")
                        .documentationCount(5)
                        .build()
        );
        
        when(documentationQueryApiService.getAllProjects()).thenReturn(mockProjects);
        
        // 执行测试
        ResponseEntity<ApiResponse<List<ProjectInfo>>> response = controller.getAllProjects();
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertTrue(response.getBody().isSuccess());
        assertEquals("查询成功", response.getBody().getMessage());
        assertEquals(1, response.getBody().getData().size());
        assertEquals("test-project", response.getBody().getData().get(0).getProjectId());
        
        verify(documentationQueryApiService, times(1)).getAllProjects();
    }
    
    @Test
    void testGetAggregatedDocumentationsByProject() {
        // 准备测试数据
        String projectId = "test-project";
        List<AggregatedDocumentationSummary> mockSummaries = List.of(
                AggregatedDocumentationSummary.builder()
                        .id("agg-doc-1")
                        .title("测试聚合说明书")
                        .summary("测试摘要")
                        .projectId(projectId)
                        .build()
        );

        when(documentationQueryApiService.getAggregatedDocumentationsByProject(projectId))
                .thenReturn(mockSummaries);

        // 执行测试（现在是POST方法）
        ResponseEntity<ApiResponse<List<AggregatedDocumentationSummary>>> response =
                controller.getAggregatedDocumentationsByProject(projectId);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertTrue(response.getBody().isSuccess());
        assertEquals(1, response.getBody().getData().size());

        verify(documentationQueryApiService, times(1))
                .getAggregatedDocumentationsByProject(projectId);
    }
    
    @Test
    void testGetMethodContent() {
        // 准备测试数据
        String methodId = "test-method-id";
        MethodContent mockMethodContent = MethodContent.builder()
                .methodId(methodId)
                .fullName("com.test.TestClass.testMethod")
                .methodName("testMethod")
                .className("TestClass")
                .content("public void testMethod() { }")
                .build();
        
        when(documentationQueryApiService.getMethodContent(methodId))
                .thenReturn(mockMethodContent);
        
        // 执行测试
        ResponseEntity<ApiResponse<MethodContent>> response = 
                controller.getMethodContent(methodId);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertTrue(response.getBody().isSuccess());
        assertEquals(methodId, response.getBody().getData().getMethodId());
        
        verify(documentationQueryApiService, times(1)).getMethodContent(methodId);
    }
    
    @Test
    void testGetMethodIdsByDocumentation() {
        // 准备测试数据
        String documentationId = "test-doc-id";
        PageRequest pageRequest = PageRequest.builder().page(1).size(10).build();
        PageResult<String> mockPageResult = PageResult.of(
                List.of("method1", "method2", "method3"),
                pageRequest,
                3L
        );
        
        when(documentationQueryApiService.getMethodIdsByDocumentation(documentationId, pageRequest))
                .thenReturn(mockPageResult);
        
        // 执行测试
        ResponseEntity<ApiResponse<PageResult<String>>> response = 
                controller.getMethodIdsByDocumentation(documentationId, pageRequest);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertTrue(response.getBody().isSuccess());
        assertEquals(3, response.getBody().getData().getData().size());
        assertEquals(3L, response.getBody().getData().getTotal());
        
        verify(documentationQueryApiService, times(1))
                .getMethodIdsByDocumentation(documentationId, pageRequest);
    }
    
    @Test
    void testErrorHandling() {
        // 测试异常处理
        when(documentationQueryApiService.getAllProjects())
                .thenThrow(new RuntimeException("测试异常"));
        
        ResponseEntity<ApiResponse<List<ProjectInfo>>> response = controller.getAllProjects();
        
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("查询失败"));
    }
}
