# 第3层调用关系技术文档

## 1. 概述

- **整体功能描述**：实现财付通新解冻请求的端到端处理流程，涵盖请求参数构建、国密加密签名、HTTP请求执行、响应解析及异常处理
- **核心业务流程**：
1. 接收业务参数并构建标准财付通请求对象
2. 执行国密(SM2/SM4)加密和数字签名
3. 通过HTTP客户端发送请求（支持重试机制）
4. 解析响应数据并执行解密验签
5. 统一化错误码映射和结果返回
- **主要技术栈**：Spring RetryTemplate、Apache HttpClient、国密算法(SM2/SM4)、Jackson/Fastjson序列化、SLF4J日志
- **文档范围**：深入分析59个方法组成的完整调用链，聚焦加密通信、重试机制和异常处理

## 2. 架构设计

- **模块划分和职责**：
| 模块 | 职责 | 关键类 |
|---|---|---|
| 请求处理层 | 参数接收与路由 | `TenpayRequestHandler` |
| 业务服务层 | 请求构建与结果处理 | `TenpayRequestServiceImpl` |
| HTTP代理层 | 加密/签名/网络通信 | `TenpayHttpClientProxy` |
| 基础服务层 | HTTP请求执行 | `TenpayHttpBaseService` |
| 加密组件 | 国密算法实现 | `Sm2AndSm4Encryptor` |

- **调用关系图**：
```mermaid
graph TD
A[TenpayRequestHandler] --> B[TenpayRequestServiceImpl]
B --> C[TenpayHttpClientProxy.execute]
C --> D[createRequest]
D --> E[convertAndEncryptRequest]
E --> F[国密加密/签名]
C --> G[doHttpPostWithRetry]
G --> H[HttpClientFetcher.postHttp]
C --> I[getResponse]
I --> J[响应解密/验签]
J --> K[BaseOutput映射]
```

- **数据流向**：
- 输入：`TenpayUnFreezeNewInput`业务参数（卡号、解冻金额等）
- 处理流程：业务参数 → 请求对象 → 加密参数 → HTTP请求 → 网络传输 → 响应解析 → 解密 → 业务对象
- 输出：`TenpayUnFreezeNewOutput`含统一错误码

- **关键接口设计**：
- `BaseCftRequest`：请求对象基类（含`isRetry`重试标志）
- `Result<O>`：泛型响应封装（成功/失败状态分离）
- `EncryptField/SignField`：加解密字段注解

## 3. 核心组件详解

- **关键方法分析**：
- `execute()`：HTTP请求中枢（异常处理/重试控制/响应解析三阶段）
- `createRequest()`：实现国密加密与签名（SM2非对称加密+SM4对称加密）
- `doHttpPostWithRetry()`：基于Spring RetryTemplate的重试机制
- `decryptResponse()`：响应数据解密（动态SM4密钥处理）

- **设计模式应用**：
- 策略模式：根据`useGuomi`切换加密算法（国密/传统）
- 模板方法：`execute()`定义HTTP调用标准流程
- 工厂方法：`Sm4Util.generateKey()`生成加密密钥

- **算法实现要点**：
```java
// 国密加密核心流程
String sm4Key = Sm4Util.generateKey(); // 生成SM4密钥
String dgtlEnvlp = sm2AndSm4Encryptor.encryptSm4Key(certSN, sm4Key); // SM2加密密钥
pairs.put("dgtlEnvlp", dgtlEnvlp); // 传输数字信封
```

- **重要业务逻辑**：
- 双模式加密：通过`useGuomi`标志切换国密/传统加密（当前强制国密）
- 重试条件：特定错误码（1200|1300）触发重试机制
- 字段级加密：`@EncryptField`注解实现敏感数据自动加密

## 4. 调用链路分析

- **完整调用路径**：
1. `TenpayRequestHandler.tenpayUnFreezeNewRequest`（入口）
2. `TenpayRequestServiceImpl.createTenpayUnFreezeNewRequest`（参数映射）
3. `TenpayHttpClientProxy.execute`（核心枢纽）
4. `createRequest` → `convertAndEncryptRequest`（加密处理）
5. `doHttpPostWithRetry` → `HttpClientFetcher.postHttp`（网络请求）
6. `getResponse` → `decryptResponse`（响应处理）
7. `BaseOutput.setErrorCode`（错误码统一）

- **关键节点说明**：
- 加密转折点：`convertAndEncryptRequest`处理字段级国密加密
- 重试决策点：`request.getIsRetry()`控制重试策略选择
- 解密临界点：`decryptResponse`动态解析SM4密钥解密数据

- **数据传递过程**：
- 请求阶段：业务对象 → 字段映射 → 加密转换 → 参数字典 → HTTP实体
- 响应阶段：原始响应 → Map解析 → 错误码映射 → 字段解密 → 业务对象

- **性能考虑点**：
- 加密开销：国密算法计算密集，大请求体需关注CPU消耗
- 反射瓶颈：`createRequest()`大量使用反射操作字段
- 重试放大：网络不稳定时重试机制可能加剧延迟（默认无退避策略）

## 5. 异常处理机制

- **错误处理策略**：
| 异常类型 | 处理方式 | 错误码 |
|---|---|---|
| `BusinessException` | 记录日志返回业务错误 | 自定义业务码 |
| `IllegalAccessException` | 返回系统错误 | SYS_FAIL |
| 网络超时 | 触发重试机制 | HTTP_RET_CODE=1 |

- **异常传播路径**：
- 方法内捕获 → 日志记录（含请求参数） → 封装Result对象 → 上层设置ErrorCode

- **容错机制**：
- 重试容错：对网络波动和特定错误码自动重试（最大次数可配）
- 安全降级：加密失败时回退原始数据（`encryptWithAes`）
- 结果兜底：HTTP请求异常返回最后有效响应（`recoverContext`）

- **恢复和重试逻辑**：
```java
retryTemplate.execute(retryContext -> {
if(retryContext.getRetryCount()>0) log.error("第{}次重试", retryCount);
Map map = doHttpPost(url, params);
if(needResend(rescode)) throw new BusinessException("触发重试"); // 重试条件判断
return map;
}, recoverContext -> recoverContext.getAttribute("map")); // 恢复策略
```

## 6. 技术实现细节

- **关键技术选型**：
- 网络层：Apache HttpClient 4.x（连接池管理）
- 加密层：BouncyCastle国密实现（SM2/SM4）
- 重试机制：Spring RetryTemplate（同步重试）

- **第三方依赖**：
- 加密提供者：`org.bouncycastle`（国密算法支持）
- 配置中心：`AppConfigManager`动态获取参数
- 序列化：Fastjson（日志输出）/ Jackson（对象映射）

- **关键配置项**：
| 配置项 | 作用 | 示例值 |
|---|---|---|
| `tenpay_unfreeze_new_request_url` | 解冻接口URL | https://api.tenpay.com/unfreeze |
| `cft_wepay_version` | 接口版本号 | 1.0 |
| `guomi.sm4.iv` | SM4初始向量 | BASE64字符串 |
| `need_resend_res_code` | 重试触发错误码 | 1200|1300 |

- **环境要求**：
- 证书配置：微众/财付通国密证书（`webank.guomi.cert-sn`）
- 网络策略：财付通API白名单访问权限
- 加密支持：BouncyCastle Provider注册

## 优化建议

1. **反射优化**：
- 用预编译字段映射替代运行时反射（如MapStruct）
- 缓存`Class.getDeclaredFields()`结果减少反射开销

2. **重试策略增强**：
- 增加指数退避算法：`ExponentialBackOffPolicy`
- 添加熔断机制（如Resilience4j）

3. **国密开关配置化**：
- 移除硬编码`useGuomi=true`，改为配置中心动态切换

4. **日志优化**：
- 敏感数据脱敏（加密字段不打印原始值）
- 减少高频日志（如`getReqeustString`）

5. **资源释放**：
- 使用try-with-resources确保`CloseableHttpResponse`关闭

6. **验签实现**：
- 补全`verifySignResponse`方法增强响应安全性
