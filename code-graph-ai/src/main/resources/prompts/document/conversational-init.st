# 多轮对话文档生成任务说明

你好！我需要为一个Java项目生成完整的技术文档。由于代码量较大，我会采用分批分析的方式进行。

## 项目基本信息
- **入口点方法**: {entryPoint}
- **分析层级**: 第{level}层
- **总方法数**: {methodCount}个
- **项目ID**: {projectId}
- **分支**: {branchName}

## 分批分析流程
我会将代码分成{batchCount}个批次进行分析：

### 工作方式
1. **分批处理**: 每轮对话分析一个批次的代码
2. **上下文传递**: 我会主动提供之前所有轮次的分析结果作为上下文
3. **渐进分析**: 基于已有分析结果，逐步深入理解整个系统
4. **最终整合**: 所有批次分析完成后，生成完整连贯的技术文档

### 你的任务
- **专注当前批次**: 重点分析当前批次的代码内容
- **参考历史分析**: 结合我提供的历史分析结果，理解代码间的关联
- **保持一致性**: 确保分析风格和深度与之前保持一致
- **识别关联**: 主动识别当前批次与之前分析内容的关联关系

## 分析重点
请在每轮分析中关注：
- **功能职责**: 每个方法/类的具体功能和作用
- **调用关系**: 方法间的调用链路和依赖关系
- **技术特点**: 使用的技术栈、设计模式、算法等
- **业务逻辑**: 核心业务规则和处理流程
- **异常处理**: 错误处理机制和边界情况
- **性能考虑**: 潜在性能瓶颈和优化点
- **架构设计**: 模块划分和职责分离

## 输出要求
- **语言**: 使用中文
- **格式**: Markdown格式
- **结构**: 清晰的层次结构
- **详细度**: 提供足够的技术细节，便于理解和维护
- **连贯性**: 与历史分析结果保持风格和深度的一致性

## 重要说明
- 我会在每轮对话中提供完整的上下文信息，你无需依赖记忆
- 请专注于当前批次的分析，同时考虑与历史分析的关联
- 如有疑问或需要澄清，请及时提出

现在开始第一轮代码分析。
